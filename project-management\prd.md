🤔 项目目标
一句话：把若干 \*.txt 放进指定目录 → 运行脚本 → 访问 /chat.html → 提问 → 得到仅基于这些 TXT 内容的答案。我会给你一个 openai 的代理商的 api 和代理访问链接。llm 用 gpt-4o-mini,向量模型用 openai 的。text-embedding-3-small

无需注册：任何人可直接聊

单页面：浏览器只有一个聊天页

检索：关键词 (BM25) ⊕ 向量检索 (COS sim. on embeddings)

非流式输出：一次性返回完整回答

前后端分离：后端 FastAPI 在 api.example.com，前端静态页在 chat.example.org

技术栈

后端：Python 3.11 · LlamaIndex ≥ 0.11 · ChromaDB (sqlite) · FastAPI

前端：纯 HTML + TailwindCSS CDN + Vanilla JS（

🗺️ 总体架构

新 TXT ➜ 分块 ➜ 生成 BM25 词频索引 + Sentence-Transformers 向量 ➜ 存入 Chroma

Python 3.11.13

llama-index 0.12.42

chromadb 1.0.12

fastapi 0.115.13

uvicorn[standard] 0.34.3

pydantic-settings 2.9.1 2025-04-18
pypi.org
